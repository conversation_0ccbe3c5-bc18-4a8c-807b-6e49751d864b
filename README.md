# 双外脑自主决策提示词系统 v4.0

## 项目概述

这是一个基于双外脑架构的AI自主决策提示词系统，通过CogniGraph™（认知图迹）和ArchGraph™（架构图系统）的协同工作，实现了从"聊天式开发"到"工程化协作"的升级。

## 🧠 核心创新：双外脑架构

### CogniGraph™ 认知图迹
- **功能**：管理思考过程、决策记录、任务状态
- **文件**：`projectX.cognigraph.json`
- **专注**：认知轨迹（需求→分析→决策→执行→结果）

### ArchGraph™ 架构图系统  
- **功能**：管理系统架构、模块关系、架构演进
- **文件**：`projectX.archgraph.json`
- **专注**：结构轨迹（组件→关系→架构→演进→优化）

### 协同机制
- **决策同步**：架构决策与认知决策双向同步
- **状态映射**：进度状态、任务状态、质量状态一致性保证
- **更新触发**：需求变更、技术选型、任务完成自动触发同步

## 🏗️ 系统架构

### 8层架构设计
1. **📥 输入层**：用户需求接收、项目上下文加载
2. **⚖️ 决策层**：复杂度判断、架构复杂度评估、路径选择
3. **🔍 分析层**：需求深度分析、信息收集验证、约束识别
4. **🧠 双外脑设计层**：CogniGraph + ArchGraph 协同创建
5. **📋 规划层**：架构驱动设计、方案规划、任务分解
6. **⚡ 执行层**：工具编排、代码实现、架构验证
7. **🔍 质量层**：多维质量检查、架构质量评估
8. **📤 输出层**：文档生成、架构图输出、知识沉淀

### 工具生态系统
- **GitHub工具集**：代码仓库管理、协作开发
- **Playwright工具集**：浏览器自动化、Web测试
- **Tavily工具集**：网络搜索、内容提取
- **Context7工具集**：技术文档、API参考
- **MasterGo工具集**：设计文件转代码
- **Sequential Thinking**：复杂问题分析
- **Mermaid工具**：架构图生成

## 📋 开发进度

### ✅ 已完成
- [x] v0.003 基础提示词系统设计
- [x] 系统架构分析和重新设计
- [x] CogniGraph™ 认知图迹系统设计
- [x] ArchGraph™ 架构图系统设计
- [x] 双外脑协同机制设计
- [x] 完整的8层架构流程设计
- [x] v4.0 双外脑提示词系统实现
- [x] 架构图可视化（Mermaid）
- [x] JSON数据结构定义
- [x] 工作流程详细说明

### 🔄 进行中
- [ ] 系统测试和验证
- [ ] 使用案例编写
- [ ] 性能优化

### 📅 待处理
- [ ] 多项目协同管理
- [ ] 更多外部工具集成
- [ ] 用户界面开发
- [ ] 部署和运维工具

## 📁 文件结构

```
/
├── v4.0-双外脑自主决策提示词系统.md    # 完整提示词系统
├── projectX.archgraph.json              # ArchGraph架构图系统
├── v0.003-自主决策提示词系统.md         # 原始版本（参考）
├── README.md                            # 项目说明文档
└── 架构图/
    ├── 双外脑系统架构图.mermaid
    ├── ArchGraph详细结构图.mermaid
    └── 双外脑协同流程图.mermaid
```

## 🚀 使用方法

### 1. 系统启动
```
1. 用户提出需求
2. 系统自动检查项目目录
3. 加载或创建双外脑文件
4. 执行复杂度判断
5. 选择处理路径（简单/复杂）
```

### 2. 复杂任务处理流程
```
输入层 → 决策层 → 分析层 → 双外脑设计层 → 
规划层 → 执行层 → 质量层 → 输出层
```

### 3. 简单任务快速通道
```
输入 → 判断 → 直接执行 → 简单架构记录 → 完成
```

## 🎯 核心优势

1. **双外脑协同**：认知过程 + 架构结构的完整记录
2. **架构可视化**：自动生成和管理多视图架构图
3. **全局理解**：清晰了解系统全貌和组件关系
4. **演进管理**：追踪架构变化，支持版本管理
5. **质量保证**：双重质量检查，确保高质量交付
6. **知识沉淀**：架构经验和认知经验双重积累

## 🔧 技术特点

- **结构化**：基于JSON的结构化数据存储
- **可视化**：Mermaid图表自动生成
- **可追溯**：完整的决策和演进历史
- **可扩展**：模块化设计，支持功能扩展
- **高效率**：Token优化，避免冗余信息

## 📊 质量指标

### 架构质量
- 模块化程度：高
- 耦合度：低
- 可扩展性：强
- 可维护性：优

### 流程质量
- 完整性：100%
- 可追溯性：完整
- 异常处理覆盖率：全面
- 质量门禁：严格

## 🔄 版本历史

- **v1.0**：基础CogniGraph系统
- **v2.0**：工作流程优化
- **v3.0**：双外脑架构设计
- **v4.0**：完整系统实现

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 📄 许可证

MIT License

## 📞 联系方式

如有问题或建议，请通过 Issue 联系。

---

**项目状态**：✅ 架构设计完成，系统实现完成
**下一步**：系统测试和实际应用验证
